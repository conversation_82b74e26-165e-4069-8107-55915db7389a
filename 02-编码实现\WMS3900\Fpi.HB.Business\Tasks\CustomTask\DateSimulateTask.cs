using System;
using Fpi.Data.Config;
using Fpi.Util.EnumRelated;

namespace Fpi.HB.Business.Tasks
{
    /// <summary>
    /// 数据模拟任务（开发阶段用）
    /// </summary>
    public class DateSimulateTask : CustomTask
    {
        #region 字段属性

        private static readonly Random _rd = new Random();

        #endregion

        #region 构造

        public DateSimulateTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "数据模拟任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "DateSimulateTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "数据模拟任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "数据模拟任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            foreach(ValueNode valueNode in DataManager.GetInstance().GetAllValueNodes())
            {
                valueNode.State = EnumOperate.GetRandomEnum(typeof(eValueNodeState));

                if(valueNode.Fixup == null)
                {
                    try
                    {
                        int i = 0;
                        double v;
                        if(valueNode.DefaultAlarmLimit != null)
                        {
                            do
                            {
                                v = _rd.Next((int)valueNode.DefaultAlarmLimit.alarmLower * 100, (int)Math.Ceiling(valueNode.DefaultAlarmLimit.alarmUpper) * 100) * 0.01;
                                i++;
                            } while(valueNode.IsOverAlarmLimit(v) && i < 10);
                        }
                        else
                        {
                            v = _rd.Next(100, 10000) * 0.01;
                        }
                        valueNode.SetValue(v);
                    }
                    catch
                    {
                        // ignored
                    }
                }
            }
        }

        #endregion
    }
}
